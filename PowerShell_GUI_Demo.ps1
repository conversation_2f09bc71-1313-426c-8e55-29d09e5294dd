# PowerShell GUI 演示脚本
# 包含输入框、按钮、编辑框和消息框交互功能

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 创建主窗体
$form = New-Object System.Windows.Forms.Form
$form.Text = "PowerShell GUI 演示"
$form.Size = New-Object System.Drawing.Size(500, 400)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = "FixedDialog"
$form.MaximizeBox = $false

# 创建标签 - 输入框说明
$labelInput = New-Object System.Windows.Forms.Label
$labelInput.Location = New-Object System.Drawing.Point(20, 20)
$labelInput.Size = New-Object System.Drawing.Size(200, 20)
$labelInput.Text = "请在下方输入内容："
$form.Controls.Add($labelInput)

# 创建输入框
$textBoxInput = New-Object System.Windows.Forms.TextBox
$textBoxInput.Location = New-Object System.Drawing.Point(20, 50)
$textBoxInput.Size = New-Object System.Drawing.Size(300, 25)
$textBoxInput.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$form.Controls.Add($textBoxInput)

# 创建按钮
$buttonProcess = New-Object System.Windows.Forms.Button
$buttonProcess.Location = New-Object System.Drawing.Point(340, 48)
$buttonProcess.Size = New-Object System.Drawing.Size(100, 30)
$buttonProcess.Text = "处理内容"
$buttonProcess.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$buttonProcess.BackColor = [System.Drawing.Color]::LightBlue
$form.Controls.Add($buttonProcess)

# 创建标签 - 输出框说明
$labelOutput = New-Object System.Windows.Forms.Label
$labelOutput.Location = New-Object System.Drawing.Point(20, 100)
$labelOutput.Size = New-Object System.Drawing.Size(200, 20)
$labelOutput.Text = "输出结果："
$form.Controls.Add($labelOutput)

# 创建多行编辑框（输出区域）
$textBoxOutput = New-Object System.Windows.Forms.TextBox
$textBoxOutput.Location = New-Object System.Drawing.Point(20, 130)
$textBoxOutput.Size = New-Object System.Drawing.Size(420, 150)
$textBoxOutput.Multiline = $true
$textBoxOutput.ScrollBars = "Vertical"
$textBoxOutput.ReadOnly = $true
$textBoxOutput.Font = New-Object System.Drawing.Font("Consolas", 10)
$textBoxOutput.BackColor = [System.Drawing.Color]::LightGray
$form.Controls.Add($textBoxOutput)

# 创建清空按钮
$buttonClear = New-Object System.Windows.Forms.Button
$buttonClear.Location = New-Object System.Drawing.Point(20, 300)
$buttonClear.Size = New-Object System.Drawing.Size(80, 30)
$buttonClear.Text = "清空"
$buttonClear.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$buttonClear.BackColor = [System.Drawing.Color]::LightCoral
$form.Controls.Add($buttonClear)

# 创建退出按钮
$buttonExit = New-Object System.Windows.Forms.Button
$buttonExit.Location = New-Object System.Drawing.Point(360, 300)
$buttonExit.Size = New-Object System.Drawing.Size(80, 30)
$buttonExit.Text = "退出"
$buttonExit.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$buttonExit.BackColor = [System.Drawing.Color]::LightSalmon
$form.Controls.Add($buttonExit)

# 按钮点击事件处理
$buttonProcess.Add_Click({
    $inputText = $textBoxInput.Text.Trim()
    
    if ([string]::IsNullOrEmpty($inputText)) {
        # 显示警告消息框
        [System.Windows.Forms.MessageBox]::Show(
            "请先输入一些内容！", 
            "提示", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Warning
        )
        $textBoxInput.Focus()
        return
    }
    
    # 获取当前时间
    $currentTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    # 在输出框中显示处理结果
    $outputText = @"
=== 处理时间: $currentTime ===
输入内容: $inputText
内容长度: $($inputText.Length) 个字符
内容类型: $(if ($inputText -match '^\d+$') { '纯数字' } elseif ($inputText -match '^[a-zA-Z]+$') { '纯英文' } elseif ($inputText -match '^[\u4e00-\u9fa5]+$') { '纯中文' } else { '混合内容' })
反转内容: $(-join $inputText.ToCharArray()[($inputText.Length-1)..0])

"@
    
    # 追加到输出框
    if ($textBoxOutput.Text.Length -gt 0) {
        $textBoxOutput.Text += "`r`n" + $outputText
    } else {
        $textBoxOutput.Text = $outputText
    }
    
    # 滚动到底部
    $textBoxOutput.SelectionStart = $textBoxOutput.Text.Length
    $textBoxOutput.ScrollToCaret()
    
    # 显示成功消息框
    $result = [System.Windows.Forms.MessageBox]::Show(
        "内容处理完成！`n`n输入内容: $inputText`n处理时间: $currentTime", 
        "处理成功", 
        [System.Windows.Forms.MessageBoxButtons]::OK, 
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    # 清空输入框并聚焦
    $textBoxInput.Clear()
    $textBoxInput.Focus()
})

# 清空按钮事件
$buttonClear.Add_Click({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要清空所有内容吗？", 
        "确认清空", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
        $textBoxInput.Clear()
        $textBoxOutput.Clear()
        $textBoxInput.Focus()
        
        [System.Windows.Forms.MessageBox]::Show(
            "内容已清空！", 
            "操作完成", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Information
        )
    }
})

# 退出按钮事件
$buttonExit.Add_Click({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要退出程序吗？", 
        "确认退出", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
        $form.Close()
    }
})

# 回车键快捷处理
$textBoxInput.Add_KeyDown({
    if ($_.KeyCode -eq [System.Windows.Forms.Keys]::Enter) {
        $buttonProcess.PerformClick()
    }
})

# 窗体关闭事件
$form.Add_FormClosing({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要关闭程序吗？", 
        "确认关闭", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::No) {
        $_.Cancel = $true
    }
})

# 设置初始焦点
$textBoxInput.Focus()

# 显示窗体
Write-Host "正在启动 PowerShell GUI 演示程序..."
Write-Host "功能说明："
Write-Host "1. 在输入框中输入任意内容"
Write-Host "2. 点击'处理内容'按钮或按回车键"
Write-Host "3. 查看输出框中的处理结果"
Write-Host "4. 程序会弹出消息框显示处理状态"
Write-Host "5. 使用'清空'按钮清除所有内容"
Write-Host "6. 使用'退出'按钮或关闭窗口退出程序"
Write-Host ""

$form.ShowDialog() | Out-Null